package com.hmit.kernespring.modules.matter_application.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.google.gson.*;
import com.google.gson.reflect.TypeToken;
import com.hmit.kernespring.common.annotation.SysLog;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.common.utils.*;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.app.service.APIService;
import com.hmit.kernespring.modules.cjrone.entity.CjroneAdministrativeDivisionEntity;
import com.hmit.kernespring.modules.cjrone.entity.CjroneCertificateApplyDocEntity;
import com.hmit.kernespring.modules.cjrone.entity.CjroneDocumentEntity;
import com.hmit.kernespring.modules.cjrone.entity.CjroneSignatureEntity;
import com.hmit.kernespring.modules.cjrone.service.*;
import com.hmit.kernespring.modules.data_management.entity.*;
import com.hmit.kernespring.modules.data_management.service.ApiCardIdService;
import com.hmit.kernespring.modules.data_management.service.DataDeadinfoService;
import com.hmit.kernespring.modules.data_management.service.DataDisabilityCertificateService;
import com.hmit.kernespring.modules.data_management.service.DisabilityCertificateSyncDataService;
import com.hmit.kernespring.modules.matter_application.entity.DisabilityAssessmentCategoryEntity;
import com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity;
import com.hmit.kernespring.modules.matter_application.service.*;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import com.hmit.kernespring.modules.sys.controller.AbstractController;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import com.itextpdf.text.BadElementException;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Image;
import com.itextpdf.text.pdf.*;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Array;
import java.lang.reflect.Type;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * 残疾证申请表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-04 16:03:42
 */
@RestController
@RequestMapping("matter_application/disabilitycertificateapplication")
public class DisabilityCertificateApplicationController extends AbstractController {

    @Autowired
    private CjroneStreetService cjroneStreetService;

    @Autowired
    private ApiCardIdService apiCardIdService;

    @Autowired
    private DataDisabilityCertificateService dataDisabilityCertificateService;
    @Autowired
    private APIService apiService;
    @Autowired
    private DisabilityCertificateApplicationService disabilityCertificateApplicationService;

    @Autowired
    private CjroneProperties cjroneProperties;

    @Autowired
    private CjroneWelfareMatterApplicationService cjroneWelfareMatterApplicationService;

    @Autowired
    private DataDeadinfoService dataDeadinfoService;



//<editor-fold> 北仑修改代码   该模块主要用来验证

    /**
     *  2020/04/22 bob
     * 1、验证是否北仑户籍或死亡 2、是否有残疾证 调用公安身份证和户籍接口，及查询本系统后台数据
     */
    @RequestMapping("/checkIDCard")
    public R checkIDCard(@RequestBody DataDisabilityCertificateEntity de){
        System.out.println("checkidcard---->"+de.getIdCard());

        boolean is_dead = false;  //是否已死亡
        boolean is_bl = false;   //是否北仑户籍
        boolean is_cjz = false;  //是否持有残疾证
        boolean is_dx = false;  //是否申请了电信爱心24小时
        boolean is_yd = false;  //是否申请了移动爱心24小时
        boolean is_dxhj = false;  //是否是大榭户籍（不可申请爱心24小时）


        Map<String,Object> nmap = new HashMap<>();
        nmap.put("idCard",de.getIdCard());
        Map<String,Object> result_map = cjroneWelfareMatterApplicationService.queryMattersByMap(nmap);

        if(result_map != null){
            if (!"0".equals(result_map.get("isLove24dx").toString())){
                is_dx = true;
            }

            if (!"0".equals(result_map.get("isLove24yd").toString())){
                is_yd = true;
            }
        }

        // <editor-fold> 判断是否持有残疾证 && 判断是否符合爱心24小时的申请条件--户籍不包含北仑大榭开发区
        DataDisabilityCertificateEntity dataDisabilityCertificateEntity=new DataDisabilityCertificateEntity();
        Map<String, Object> dis_tmp = new HashMap<>();
        dis_tmp.put("id_card", de.getIdCard());
        List<DataDisabilityCertificateEntity> dis_list=(List<DataDisabilityCertificateEntity>) dataDisabilityCertificateService.listByMap(dis_tmp);
        if(dis_list!=null && dis_list.size()>0){
            // 验证为持证残疾人
            is_cjz=true;
            // 可以放心的取第一条记录，因为多重残疾已合并
            dataDisabilityCertificateEntity=dis_list.get(0);

            if(dis_list.get(0).getNativePlace().contains("大榭")){
                //is_dxhj = true;
            }
        }
        // </editor-fold>

        // <editor-fold> 判断是否北仑户籍，优先从本地数据判断  此 apiCardId中的数据需每晚更新 ，若本地无数据则从接口中获取，同时获得电子证照
        ApiCardIdEntity apiCardId_result = new ApiCardIdEntity();
        List<ApiCardIdEntity> is_bl_list = (List<ApiCardIdEntity>) apiCardIdService.listByMap(dis_tmp);
        //本地存在数据
        if(is_bl_list!=null && is_bl_list.size()>0){
            if("北仑区公安局".equals(is_bl_list.get(0).getQfjg())){
                is_bl=true;
            }
            apiCardId_result=is_bl_list.get(0);
        }else{
           /* // 本地无此数据，需要立即调用接口获取
            Map<String, Object> blhh_result = apiService.queryHuJiInfo(de.getIdCard(), null);
            if ("00".equals(blhh_result.get("code").toString()) && blhh_result.get("datas") != null && !"[]".equals(blhh_result.get("datas").toString())) {
                String data = blhh_result.get("datas").toString();
                data = data.substring(1,data.length()-1);
                String data_new = data.substring(0,data.indexOf("registrationDate=")+27)+data.substring(data.indexOf("registrationDate=")+36,data.length());
                System.out.println(data_new);
                ApiHuJiEntity apiHuJiEntity = new Gson().fromJson(data_new.replaceAll(" ","").replaceAll("=,","=null,"), ApiHuJiEntity.class);
                if (apiHuJiEntity != null) {
                    ApiCardIdEntity apiCardId = new ApiCardIdEntity();
                    System.out.println(new Gson().toJson(apiHuJiEntity));
                    // 北仑户籍
                    if (apiHuJiEntity.getRegistrationAuthority() != null && apiHuJiEntity.getRegistrationAuthority().indexOf("北仑") != -1){
                        System.out.println("当前是北仑户籍");
                        is_bl = true;
                        apiCardId.setQfjg("北仑区公安局");
                    }else {
                        apiCardId.setQfjg(apiHuJiEntity.getRegistrationAuthority());
                    }
                    System.out.println("apiHuJiEntity: "+new Gson().toJson(apiHuJiEntity));

                    // 开始保存数据至本地
                    apiCardId.setName(apiHuJiEntity.getName());
                    apiCardId.setSex(apiHuJiEntity.getSex());
                    apiCardId.setNationality(apiHuJiEntity.getNation());
                    if (apiHuJiEntity.getDateOfBirth().length() == 8){
                        String aabc = apiHuJiEntity.getDateOfBirth();
                        aabc = aabc.substring(0,4)+"-"+aabc.substring(4,6)+"-"+aabc.substring(6,8);
                        apiCardId.setBirthday(aabc);
                    }else {
                        apiCardId.setBirthday(apiHuJiEntity.getDateOfBirth());
                    }
                    apiCardId.setNativePlace("浙江");
                    apiCardId.setIdCard(apiHuJiEntity.getIdcard());
                    apiCardId.setNativeAddress(apiHuJiEntity.getWhereToLocal());
                    apiCardId.setJgbh("长期");
                    System.out.println("dATABean: "+new Gson().toJson(apiHuJiEntity));
                    // 开始下载电子照片
                    Map<String, Object> photo_result = apiService.queryCardIdPho(de.getIdCard(), apiHuJiEntity.getName(), apiHuJiEntity.getNation(), apiHuJiEntity.getDateOfBirth(), null);
                    System.out.println("photo_result: "+photo_result);
                    if (photo_result.get("datas") == null || "null".equals(photo_result.get("datas"))){
                        apiCardId.setPhoto(null);
                    }else {
                        System.out.println(photo_result.get("datas").toString());
                        String pho_datas = photo_result.get("datas").toString();
                        System.out.println("当前电子照片张数：" + count(pho_datas, "uRL"));
                        String is_exist_path = null;
                        if (count(pho_datas, "uRL") > 0) {
                            is_exist_path = pho_datas.substring(StringUtils.ordinalIndexOf(pho_datas, "uRL", count(pho_datas, "uRL")) + 4, pho_datas.length() - 2);
                        }
                        System.out.println("is_exist_path" + is_exist_path);
                        if (is_exist_path != null) {
                            String sava_path = HttpRequestUtil.downloadPicture(is_exist_path, cjroneProperties.getDownloadPath(), apiCardId.getIdCard() + ".jpg");
                            apiCardId.setPhoto(sava_path);
                        }
                    }
                    apiCardId_result = apiCardId;
                    //将接口获得的数据保存至数据库
                    apiCardIdService.save(apiCardId);
                }
            }*/

            //以下的代码表示从临时数据接口中去获取
            Map<String, Object> blhh_result = apiService.querrHuKouBenInfo(de.getIdCard(), null);
            if ("00".equals(blhh_result.get("code").toString()) && blhh_result.get("datas") != null && !"[]".equals(blhh_result.get("datas").toString())) {
                String data = blhh_result.get("datas").toString();
                data = data.substring(1,data.length()-1);
                //String data_new = data.substring(0,data.indexOf("registrationDate=")+27)+data.substring(data.indexOf("registrationDate=")+36,data.length());
                System.out.println(data);

                ApiHuKouBenEntity apiHuKouBenEntity = new Gson().fromJson(data.replaceAll(" ","").replaceAll("=,","=null,").replaceAll("=}","=null}"), ApiHuKouBenEntity.class);
                if (apiHuKouBenEntity != null) {
                    ApiCardIdEntity apiCardId = new ApiCardIdEntity();
                    System.out.println(new Gson().toJson(apiHuKouBenEntity));
                    // 北仑户籍
                    if (apiHuKouBenEntity.getCzrkpcsmc()  != null && (apiHuKouBenEntity.getCzrkpcsmc().indexOf("北仑") != -1||apiHuKouBenEntity.getCzrkpcsmc().indexOf("大榭") != -1)){
                        System.out.println("当前是北仑户籍");
                        is_bl = true;
                        apiCardId.setQfjg("北仑区公安局");
                    }else {
                        apiCardId.setQfjg(apiHuKouBenEntity.getCzrkpcsmc());
                    }
                    System.out.println("apiHuJiEntity: "+new Gson().toJson(apiHuKouBenEntity));

                    // 开始保存数据至本地
                    apiCardId.setName(apiHuKouBenEntity.getCzrkxm());
                    apiCardId.setSex(apiHuKouBenEntity.getCzrkxb());
                    apiCardId.setNationality(apiHuKouBenEntity.getCzrkjgssx());
                    if (apiHuKouBenEntity.getCzrkcsrq().length() == 8){
                        String aabc = apiHuKouBenEntity.getCzrkcsrq();
                        aabc = aabc.substring(0,4)+"-"+aabc.substring(4,6)+"-"+aabc.substring(6,8);
                        apiCardId.setBirthday(aabc);
                    }else {
                        apiCardId.setBirthday(apiHuKouBenEntity.getCzrkcsrq());
                    }
                    apiCardId.setNativePlace("浙江");
                    apiCardId.setIdCard(apiHuKouBenEntity.getCzrkgmsfhm());
                    apiCardId.setNativeAddress(apiHuKouBenEntity.getCzrkzz());
                    apiCardId.setJgbh("长期");
                    System.out.println("dATABean: "+new Gson().toJson(apiHuKouBenEntity));
                    // 开始下载电子照片
                    Map<String, Object> photo_result = apiService.queryCardIdPho(de.getIdCard(), apiHuKouBenEntity.getCzrkxm(), apiHuKouBenEntity.getCzrkmz(), apiHuKouBenEntity.getCzrkcsrq(), null);
                    System.out.println("photo_result: "+photo_result);
                    if (photo_result.get("datas") == null || "null".equals(photo_result.get("datas"))){
                        apiCardId.setPhoto(null);
                    }else {
                        System.out.println(photo_result.get("datas").toString());
                        String pho_datas = photo_result.get("datas").toString();
                        System.out.println("当前电子照片张数：" + count(pho_datas, "uRL"));
                        String is_exist_path = null;
                        if (count(pho_datas, "uRL") > 0) {
                            is_exist_path = pho_datas.substring(StringUtils.ordinalIndexOf(pho_datas, "uRL", count(pho_datas, "uRL")) + 4, pho_datas.length() - 2);
                        }
                        System.out.println("is_exist_path" + is_exist_path);
                        if (is_exist_path != null) {
                            String sava_path = HttpRequestUtil.downloadPicture(is_exist_path, cjroneProperties.getDownloadPath(), apiCardId.getIdCard() + ".jpg");
                            apiCardId.setPhoto(sava_path);
                        }
                    }
                    apiCardId_result = apiCardId;
                    //将接口获得的数据保存至数据库
                    apiCardIdService.save(apiCardId);
                }
            }

        }
        // </editor-fold>

        // <editor-fold> 判断是否死亡  从本地判断  ---  目前从接口获取  死亡表中的数据通过每晚定时任务查询
        List<DataDeadinfoEntity> dead_list=(List<DataDeadinfoEntity>) dataDeadinfoService.listByMap(dis_tmp);
        if(dead_list.size()>0){
            is_dead=true;
        }
        // </editor-fold> （）

        // <editor-fold> 返回后台校验完成后的数据结果
        if (apiCardId_result.getId() == null){
            // 判断电子照片有没有
            boolean isImgUrl = true; // 默认照片存在
            String file_path = cjroneProperties.getDownloadPath()+de.getIdCard() + ".jpg";
            File filePath = new File(file_path);
            if(!filePath.exists()){
                isImgUrl= false;
            }
            return R.ok().put("msg","通过公安身份证信息接口，暂未获取到该申请人身份证信息！").put("is_bl",is_bl).put("is_dead",is_dead).put("is_disability",is_cjz).put("is_cjz",is_cjz).put("apiCardIdEntity",apiCardId_result).put("disabilityCertificateApplicationEntity",dataDisabilityCertificateEntity).put("isImgUrl",isImgUrl);
        }else {
            // 判断电子照片有没有
            boolean isImgUrl = true; // 默认照片存在
            String file_path = cjroneProperties.getDownloadPath()+de.getIdCard() + ".jpg";
            File filePath = new File(file_path);
            if(!filePath.exists()){
                isImgUrl= false;
            }
            return R.ok().put("is_bl",is_bl).put("is_dead",is_dead).put("is_disability",is_cjz).put("is_cjz",is_cjz).put("apiCardIdEntity",apiCardId_result).put("disabilityCertificateApplicationEntity",dataDisabilityCertificateEntity).put("isImgUrl",isImgUrl).put("is_dx",is_dx).put("is_yd",is_yd).put("is_dxhj",is_dxhj);
        }
        // </editor-fold>
    }


    /**
     *  2020/04/22 bob
     *  2、验证是否符合惠残政策
     */
    @RequestMapping("/infoNeD/{iDcard}")
    //@RequiresPermissions("matter_application:disabilitycertificateapplication:info")
    public R infoNeD(@PathVariable("iDcard") String iDcard){
        //根据id_card 获得残疾人信息
        DataDisabilityCertificateEntity dataDisabilityCertificateEntity=dataDisabilityCertificateService.getByIDCard(iDcard);
        if (dataDisabilityCertificateEntity == null) return new R().ok();
        //获得残疾证信息，用于判断护理补贴
        //1、去除最后一个字符
        String[] disinfo=null;
        if(dataDisabilityCertificateEntity.getDisabilityInfo()!=null){
            String disabilityinfo = dataDisabilityCertificateEntity.getDisabilityInfo().substring(0,dataDisabilityCertificateEntity.getDisabilityInfo().length()-1);
            disinfo=disabilityinfo.split(";");
        }
        //返回给前端的残疾证信息
        List<Map<String,Object>> dcdisable_list=new ArrayList<>();

        //判断是否已经申请过惠残 及 一些基本条件
        Map<String,Object> nmap = new HashMap<>();
        nmap.put("idCard",iDcard);
        Map<String,Object> result_map = cjroneWelfareMatterApplicationService.queryMattersByMap(nmap);
        // 判断是否法定代表人
       Map<String, Object> doFdDbInfo = apiService.queryFDDBRInfo(iDcard, null);
        boolean is_fdDbr = false;
        if ("00".equals(doFdDbInfo.get("code").toString())) {
            System.out.println("doFdDbInfo: "+doFdDbInfo);
            if (doFdDbInfo.get("datas") != null) {
                List<ApiFdDbrEntity> list = new Gson().fromJson(doFdDbInfo.get("datas").toString(), new TypeToken<List<ApiFdDbrEntity>>() {
                }.getType());
                if (list.size() != 0) {
                    System.out.println("当前存在" + list.size() + "条法定代表人信息 第一条信息如下---> " + new Gson().toJson(list.get(0)));
                    is_fdDbr = true;
                }
            }
        }else {
            System.out.println("获取数据异常");
        }
        // 是否个体工商户
        result_map.put("is_fdDbr",is_fdDbr);

        // 根据上面已获得的数据，判断残疾人是否符合各种条件


        // <editor-fold> 生活补助金的判断条件 v2.0
        boolean isShbzjOption = true;  //默认为true
        String shbzjOptions = null;
        int age = getAgeByBirthDay(extractYearMonthDayOfIdCard(iDcard)); //获得残疾人的年龄

        // 是否已经申请了生活补助金
        if (shbzjOptions == null && !"0".equals(result_map.get("isShbzj").toString())){
            isShbzjOption = false;
            shbzjOptions = "当前人员已经申请过生活补助金!";
        } else{
            // 已享受生活困难补贴，无法申请
            if(shbzjOptions == null && !"0".equals(result_map.get("isShbt").toString())){
                isShbzjOption = false;
                shbzjOptions = "当前人员已经申请过生活补贴!";
            } else{

                // disability_certificate_sync_data -- is_fx  定时任务请求数据
                if ("1".equals(result_map.get("isFx").toString())){
                    isShbzjOption = false;
                    shbzjOptions = "当前属于服刑人员！";
                }else{

                    // 已享受 职工基本养老  无法申请
                    if(!"0".equals(result_map.get("iszhigongYangLaobx").toString())){
                        isShbzjOption = false;
                        shbzjOptions = "已享受职工基本养老";
                    }

                    // 男性 16-60 ，女性 15-55 之间无法申请
                    if("男".equals(dataDisabilityCertificateEntity.getSex())&&age>=16&&age<=60){
                        isShbzjOption = false;
                        shbzjOptions = "男性年龄在16-60之间";
                    }
                    if("女".equals(dataDisabilityCertificateEntity.getSex())&&age>=16&&age<=55){
                        isShbzjOption = false;
                        shbzjOptions = "女性年龄在16-55之间";
                    }

                    // 已享受低保 无法申请
                    if ("低保".equals(result_map.get("familyEcho").toString())){
                        isShbzjOption = false;
                        shbzjOptions = "已享受低保";
                    }
                }

            }
        }

        result_map.put("isShbzjOption",isShbzjOption);
        result_map.put("shbzjOptions",shbzjOptions);

        // </editor-fold>

        // <editor-fold> 生活补贴的判断条件 v2.0
        boolean isShbtOption = true;
        String shbtOptions = null;

      /*  if ("1".equals(result_map.get("isFx").toString())){
            isShbtOption = false;
            shbtOptions = "当前属于服刑人员！";
        }else{
            if ("1".equals(result_map.get("isTk").toString())){
                isShbtOption = false;
                shbtOptions = "当前属于特困人员！";
            }else{
                if ("1".equals(result_map.get("isGongS").toString())){
                    shbtOptions = "当前属于享受工伤保险生活护理费人员！";
                    isShbtOption = false;
                }else{
                    if ("1".equals(result_map.get("isKjEt").toString())){
                        shbtOptions = "当前属于享受困境儿童基本生活补贴政策！";
                        isShbtOption = false;
                    }else{
                        if ("无".equals(result_map.get("familyEcho").toString())){
                            shbtOptions = "非低保、低边";
                            isShbtOption = false;
                        }
                    }
                }
            }
        }
        if (shbtOptions == null && !"0".equals(result_map.get("isShbt").toString())){
            isShbtOption = false;
            shbtOptions = "当前人员已经申请过生活补贴!";
        }*/

        result_map.put("isShbtOption",isShbtOption);
        result_map.put("shbtOptions",shbtOptions);
        // </editor-fold>

        // <editor-fold> 护理补贴的判断条件 v2.0
        boolean isHlbtOption = true;
        String hlbtOptions = null;
        if ("1".equals(result_map.get("isFx").toString())){
            isHlbtOption = false;
            hlbtOptions = "当前属于服刑人员！";
        }else{
            if ("1".equals(result_map.get("isTk").toString())){
                isHlbtOption = false;
                hlbtOptions = "当前属于特困人员！";
            }else{
                if ("1".equals(result_map.get("isGongS").toString())){
                    hlbtOptions = "当前属于享受工伤保险生活护理费人员！";
                    isHlbtOption = false;
                }else{
                    String birthday = iDcard.substring(6,10)+"-"+iDcard.substring(10,12)+"-"+iDcard.substring(12,14)+" 00:00:00";
                    Date stringToDate = DateUtils.stringToDate(birthday,DateUtils.DATE_TIME_PATTERN);
                    if (DateUtils.checkAdultSix(stringToDate) && !"0".equals(result_map.get("isYangL").toString())){
                        hlbtOptions = "当前属于60周岁以上且已享受养老服务补贴政策！";
                        isHlbtOption = false;
                    }else {
                        //判断多重残疾下的情况
                        boolean isHlbtOptiondc=true;
                        if(disinfo!=null){
                            Map<String,Object> params=new HashMap<>();
                            for (String itemhos: disinfo) {
                                params.put("disableId",dataDisabilityCertificateEntity.getDisableId());
                                params.put("disabilityCategoryName",itemhos.substring(0,2));
                                params.put("disabilityDegreeName",itemhos.substring(2,4));
                                dcdisable_list.add(params);
                                if (!"智力".equals(itemhos.substring(0,2)) && !"精神".equals(itemhos.substring(0,2))){
                                    if ("三级".equals(itemhos.substring(2,4)) || "四级".equals(itemhos.substring(2,4))){
                                        hlbtOptions = "残疾等级属于三、四级，且残疾类别不属于智力、精神，不能申请！";
                                        isHlbtOptiondc = false;
                                    }
                                    else{
                                        isHlbtOptiondc = true;
                                        break;
                                    }
                                }
                                else{
                                    isHlbtOptiondc = true;
                                    break;
                                }

                            }
                        }else{
                            isHlbtOptiondc=false;
                        }

                        isHlbtOption=(isHlbtOptiondc&&isHlbtOption);
                    }
                }
            }
        }
        if (hlbtOptions == null && !"0".equals(result_map.get("isHlbt").toString())){
            isHlbtOption = false;
            hlbtOptions = "当前人员已经申请过护理补贴!";
        }

        result_map.put("isHlbtOption",isHlbtOption);
        result_map.put("hlbtOptions",hlbtOptions);
        // </editor-fold>

        // <editor-fold>  职工基本养老保险补助判断条件 v2.0
        boolean isZgjbylOptions = true;
        String zgjbylOptions = null ;
        if(!is_fdDbr){
            isZgjbylOptions = false;
            zgjbylOptions = "非个体工商户";
        }else{
            // 判断基本养老保险
            if("0".equals(result_map.get("iszhigongYangLaobx").toString())){
                isZgjbylOptions = false;
                zgjbylOptions = "非职工基本养老保险";
            }
        }

        // 判断是否已经申请过
        if (zgjbylOptions == null && !"0".equals(result_map.get("isZgjbyl").toString())){
            isZgjbylOptions = false;
            zgjbylOptions = "当前人员已经申请过职工基本养老保险补助!";
        }

        result_map.put("isZgjbylOptions",isZgjbylOptions);
        result_map.put("zgjbylOptions",zgjbylOptions);
        // </editor-fold>

        // <editor-fold> 职工基本医疗保险补助判断条件 v2.0
        boolean isZgjbylbxOptions = true;
        String zgjbylbxOptions = null;
        if(!is_fdDbr){
            isZgjbylbxOptions = false;
            zgjbylbxOptions = "非个体工商户";
        }else{
            // 判断基本医疗保险
            if("0".equals(result_map.get("iszhigongYiLiaobx").toString())){
                isZgjbylbxOptions = false;
                zgjbylbxOptions = "非职工基本医疗保险";
            }
        }

        // 判断是否已经申请过
        if (zgjbylbxOptions == null && !"0".equals(result_map.get("isZgjbylbx").toString())){
            isZgjbylbxOptions = false;
            zgjbylbxOptions = "当前人员已经申请过职工基本养老保险补助!";
        }

        result_map.put("isZgjbylbxOptions",isZgjbylbxOptions);
        result_map.put("zgjbylbxOptions",zgjbylbxOptions);

        // </editor-fold>

        // <editor-fold> 城乡居民养老保险补助判断条件 v2.0
        boolean isCxjmylOptions = true;
        String cxjmylOptions = null;

        // 判断城乡居民养老保险补助
        if("0".equals(result_map.get("ischengxiangYangLaobx").toString())){
            isCxjmylOptions = false;
            cxjmylOptions = "非城乡居民养老保险";
        }

        // 判断是否已经申请过
        if (cxjmylOptions == null && !"0".equals(result_map.get("isCxjmyl").toString())){
            isCxjmylOptions = false;
            cxjmylOptions = "当前人员已经申请过职工基本养老保险补助!";
        }

        result_map.put("isCxjmylOptions",isCxjmylOptions);
        result_map.put("cxjmylOptions",cxjmylOptions);

        // </editor-fold>

        // <editor-fold> 城乡基本医疗保险补助 v2.0
        boolean isCxjbylbxOptions = true;
        String cxjbylbxOptions = null;

        //判断残疾等级为34级
        if(disinfo!=null) {
            Map<String, Object> params = new HashMap<>();
            for (String itemhos : disinfo) {
                if ("一级".equals(itemhos.substring(2,4)) || "二级".equals(itemhos.substring(2,4))){
                    isCxjbylbxOptions = false;
                    cxjbylbxOptions = "残疾等级非3，4级";
                }
            }
        }

        // 判断参加了城乡基本医疗保险
        if("0".equals(result_map.get("ischengxiangYiLiaobx").toString())){
            isCxjbylbxOptions = false;
            cxjbylbxOptions = "非城乡基本医疗保险";
        }

        //判断是否已经申请
        if (cxjbylbxOptions == null && !"0".equals(result_map.get("isCxjbylbx").toString())){
            isCxjbylbxOptions = false;
            cxjbylbxOptions = "当前人员已经申请过职工基本养老保险补助!";
        }

        result_map.put("isCxjbylbxOptions",isCxjbylbxOptions);
        result_map.put("cxjbylbxOptions",cxjbylbxOptions);

        // </editor-fold>

        // <editor-fold> 高中教育补助

        boolean isChildeduOptions = true;
        String childeduOptions = null;

        //判断本人残疾或父母残疾

        // 判断是否已经申请

        result_map.put("isChildeduOptions",isChildeduOptions);
        result_map.put("childeduOptions",childeduOptions);

        //</editor-fold>

        // <editor-fold> 残疾大学生 学费，住宿费补助 v2.0

        boolean isCollegeeduOptions = true;
        String collegeeduOptions = null;

        //判断是否已经申请
        if (cxjbylbxOptions == null && !"0".equals(result_map.get("isCollegeedu").toString())){
            isCollegeeduOptions = false;
            collegeeduOptions = "当前人员已经申请过残疾大学生学费，住宿费补助!";
        }

        result_map.put("isCollegeeduOptions",isCollegeeduOptions);
        result_map.put("collegeeduOptions",collegeeduOptions);

        // </editor-fold>

        // <editor-fold> 残疾人康复补助 v2.0
        boolean isKfbzOptions = true;
        String kfbzOptions = null;

        //判断是否已经申请
        if (kfbzOptions == null && !"0".equals(result_map.get("isKfbz").toString())){
            isKfbzOptions = false;
            kfbzOptions = "当前人员已经申请过康复补助!";
        }

        result_map.put("isKfbzOptions",isKfbzOptions);
        result_map.put("kfbzOptions",kfbzOptions);
        //</editor-fold>

        // <editor-fold> 残疾人创业补助 v 2.0

        //判断法定年龄段
        boolean isCybzOptions = true;
        String cybzOptions = null;
        String birthday = iDcard.substring(6,10)+"-"+iDcard.substring(10,12)+"-"+iDcard.substring(12,14)+" 00:00:00";
        Date stringToDate = DateUtils.stringToDate(birthday,DateUtils.DATE_TIME_PATTERN);
        if (DateUtils.checkAdultDYSJ(stringToDate,16) && DateUtils.checkAdultXYSix(stringToDate)) {
        }else{
            cybzOptions = "当前人员年龄不属于16周岁-60周岁之间！";
            isCybzOptions = false;
        }

        //判断是否法定代表人
        if(!is_fdDbr) {
            isCybzOptions = false;
            cybzOptions = "非个体工商户";
        }

        // 判断是否已经申请
        if (cybzOptions == null && !"0".equals(result_map.get("isCybz").toString())){
            isCybzOptions = false;
            cybzOptions = "当前人员已经申请过就业创业补助!";
        }

        result_map.put("isCybzOptions",isCybzOptions);
        result_map.put("cybzOptions",cybzOptions);

        // </editor-fold>

        //<editor-fold> 精神残疾人住院补助
        boolean isZybzOptions = true;
        String zybzOptions = null;

        // 判断残疾类别是否为精神

        result_map.put("isZybzOptions",isZybzOptions);
        result_map.put("zybzOptions",zybzOptions);

        //</editor-fold>

        //<editor-fold> 残疾人医疗救助
        boolean isYljzOptions = true;
        String yljzOptions = null;

        //判断是否持证（上个接口已经判断）

        result_map.put("isYljzOptions",isYljzOptions);
        result_map.put("isYljzOptions",isYljzOptions);
        //</editor-fold>

        //<editor-fold> 残疾人临时救助 v2.0
        boolean isLsjzOptions = true;
        String lsjzOptions = null;

        //判断是否低保、低边
        if ("低保".equals(result_map.get("familyEcho").toString())){
            lsjzOptions = "当前人员享受低保";
            isLsjzOptions = false;
        }
        if("低边".equals(result_map.get("familyEcho").toString())){
            lsjzOptions = "当前人员享受低边";
            isLsjzOptions = false;
        }

        //判断是否申请
        if (lsjzOptions == null && !"0".equals(result_map.get("isLsjz").toString())){
            isLsjzOptions = false;
            lsjzOptions = "当前人员已经申请过临时救助!";
        }

        result_map.put("isLsjzOptions",isLsjzOptions);
        result_map.put("lsjzOptions",lsjzOptions);

        //</editor-fold>

        //<editor-fold> 残疾证到期换领 v2.0
        boolean isCjzhlOptions = true;
        String cjzhlOptions = null;

        result_map.put("isCjzhlOptions",isCjzhlOptions);
        result_map.put("cjzhlOptions",cjzhlOptions);


        //</editor-fold>

        //<editor-fold> 智慧 爱心24小时

        boolean isLove24Options = true;
        String love24Options = null;

        // 判断之前是否已经申请过 智慧 爱心24小时
        if (love24Options == null && !"0".equals(result_map.get("isLove24").toString())){
            isLove24Options = false;
            love24Options = "当前人员已经申请过智慧爱心24小时";
        }
        if (!DateUtils.checkAdultDYSJ(stringToDate,16)) {
            love24Options = "当前人员年龄小于16周岁！";
            isLove24Options = false;
        }


        result_map.put("isLove24Options",isLove24Options);
        result_map.put("love24Options",love24Options);

        //</editor-fold>


        //联系人固话和联系人移动电话取其一
        if("".equals(dataDisabilityCertificateEntity.getGuardianMobile())){
            dataDisabilityCertificateEntity.setGuardianMobile(dataDisabilityCertificateEntity.getGuardianTelephone());
        }

        return R.ok().put("disabilityCertificateApplication", dataDisabilityCertificateEntity).put("result_map",result_map).put("dcList",dcdisable_list);


    }

//</editor-fold>


    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss")
            .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

                @Override
                public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                    if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                        //定义为int类型,如果后台返回""或者null,则返回0
                        return null;
                    }
                    return json.getAsInt();
                }
            })
            .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

                @Override
                public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                    if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                        //定义为int类型,如果后台返回""或者null,则返回0
                        return null;
                    }
                    return json.getAsInt();
                }
            })
            .create();




    @GetMapping("getAdministrativeDivisionList")
    @ApiOperation("获得北仑区新政划分")
    public R getAdministrativeDivisionList(){
        //奉化编号330213
        List<CjroneAdministrativeDivisionEntity> adList=cjroneStreetService.getAdministrativeDivisionList("330206");
        return R.ok().put("list", adList);
    }

    @GetMapping("getAdministrativeDivisionListAll")
    @ApiOperation("获得所有省份的行政划分")
    public R getAdministrativeDivisionListAll(){
        List<CjroneAdministrativeDivisionEntity> adList=cjroneStreetService.getAdministrativeDivisionListAll("33");
        return R.ok().put("list", adList);
    }
    @GetMapping("getAdministrativeDivisionListStep")
    @ApiOperation("获得所有省份的行政划分")
    public R getAdministrativeDivisionListStep(@RequestParam Map<String, Object> params){
        System.out.println("aaaaa"+params);
        System.out.println(params.get("code"));
        System.out.println("0".equals(params.get("code").toString()));
        if (params.get("code") != null && "0".equals(params.get("code").toString())){
            List<CjroneAdministrativeDivisionEntity> adList=cjroneStreetService.getAdministrativeDivisionListStep(null,Integer.parseInt(params.get("step").toString()));
            return R.ok().put("list", adList);
        } else {
            List<CjroneAdministrativeDivisionEntity> adList=cjroneStreetService.getAdministrativeDivisionListStep(params.get("code").toString(),Integer.parseInt(params.get("step").toString()));
            return R.ok().put("list", adList);
        }
    }


    /**
     * 方法2：Java正则表达式Pattern和Matcher类
     *
     * @param srcStr
     * @param findStr
     * @return
     */
    public static int count(String srcStr, String findStr) {
        int count = 0;
        Pattern pattern = Pattern.compile(findStr);// 通过静态方法compile(String regex)方法来创建,将给定的正则表达式编译并赋予给Pattern类
        Matcher matcher = pattern.matcher(srcStr);//
        while (matcher.find()) {// boolean find() 对字符串进行匹配,匹配到的字符串可以在任何位置
            count++;
        }
        return count;
    }



    /**
     * 下载附件 zip
     */
    @RequestMapping("/downloadFj")
    public Object downloadFj(@RequestParam Map<String,Object> mapArgs )  throws IOException{
        System.out.print("id is :" +mapArgs);
        System.out.println(mapArgs.get("ids").toString());
        String ids[] = mapArgs.get("ids").toString().split("-");
        System.out.println(ids.length);

        Map<String, Object> params = new HashMap<>();
        Long current_time = System.currentTimeMillis();
        String file_path = cjroneProperties.getDownloadZipPath()+"application/"+current_time+"/";
        String file_download_path = cjroneProperties.getDownloadZipPath()+"application/"+current_time+".zip";
        System.out.println(file_path);
        params.put("download_path",file_path);

        for (String id : ids) {
            System.out.println("download:"+id);
            DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity = disabilityCertificateApplicationService.getById(id);
            File file = new File(file_path+disabilityCertificateApplicationEntity.getIdCard()+"/");
            if(!file.exists()){
                file.mkdirs();
            }
            List<File> fileList = new ArrayList<>();
            fileList.add(new File(cjroneProperties.getDownloadPath() + disabilityCertificateApplicationEntity.getIdCard()+".jpg"));
            fileList.add(new File(file_path+disabilityCertificateApplicationEntity.getIdCard() +"/" + "残疾证申请表.xls"));
            // fileList.add(new File(cjroneProperties.getDownloadPath()+"living_allowance_xiaoxiaoming.pdf"));

            // copy
            Files.copy(new File(cjroneProperties.getDownloadPath() + disabilityCertificateApplicationEntity.getIdCard()+".jpg").toPath(),new File(file_path+disabilityCertificateApplicationEntity.getIdCard() +"/"+disabilityCertificateApplicationEntity.getIdCard() +".jpg").toPath());
            // Files.copy(new File(cjroneProperties.getDownloadPath()+"living_allowance_xiaoxiaoming.pdf").toPath(),new File(file_path+id+"/"+"living_allowance_xiaoxiaoming.pdf").toPath());

            // 生成表格
            List<DisabilityCertificateApplicationEntity> applicationEntities = new ArrayList<>();
            System.out.println(new Gson().toJson(disabilityCertificateApplicationEntity));
            disabilityCertificateApplicationEntity.setSex("1".equals(disabilityCertificateApplicationEntity.getSex())? "男":"女");
            disabilityCertificateApplicationEntity.setMaritalStatus("1".equals(disabilityCertificateApplicationEntity.getMaritalStatus())? "已婚":"未婚");
            String birthday = disabilityCertificateApplicationEntity.getBirthday();
            disabilityCertificateApplicationEntity.setBirthday(birthday.substring(0,4)+"-"+birthday.substring(4,6)+"-"+birthday.substring(6,8));
            if ("1".equals(disabilityCertificateApplicationEntity.getApplicationType())){
                disabilityCertificateApplicationEntity.setApplicationType("新申请");
            }else if ("1".equals(disabilityCertificateApplicationEntity.getApplicationType())){
                disabilityCertificateApplicationEntity.setApplicationType("换领申请");
            }else if ("1".equals(disabilityCertificateApplicationEntity.getApplicationType())){
                disabilityCertificateApplicationEntity.setApplicationType("补办申请");
            }
            Map<String, Object> tmp_params = new HashMap<>();
            tmp_params.put("id_card",disabilityCertificateApplicationEntity.getIdCard());
            tmp_params.put("disability_category",disabilityCertificateApplicationEntity.getDisabilityType());

          /*  List<CjroneDisabilityHospitalEntity> hospitalEntities = (List<CjroneDisabilityHospitalEntity>) cjroneDisabilityHospitalService.listByMap(tmp_params);
            if (hospitalEntities.size()>0){
                CjroneDisabilityHospitalEntity hospitalEntity = hospitalEntities.get(0);
                disabilityCertificateApplicationEntity.setDisabilityDegreeName(hospitalEntity.getDisabilityDegree()+"级");
            }else {
                disabilityCertificateApplicationEntity.setDisabilityDegreeName("无");
            }*/
            applicationEntities.add(disabilityCertificateApplicationEntity);

            ExportParams excel_params = new ExportParams("残疾证申请表", null, "残疾证申请表");
            Workbook workbook = ExcelExportUtil.exportExcel(excel_params, DisabilityCertificateApplicationEntity.class, applicationEntities);
            FileOutputStream fos = new FileOutputStream(file_path+disabilityCertificateApplicationEntity.getIdCard() +"/" + "残疾证申请表.xls");
            workbook.write(fos);
            fos.close();
        }

        FileOutputStream fos = new FileOutputStream(new File(file_download_path));

        ZipUtils.toZip(file_path, fos, true);
        return R.ok().put("file_download_path",current_time+".zip");
    }


    public static int getAgeByBirthDay(String birthDay){
        if (birthDay == null || birthDay.length()<4) {
            return 0;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        //得到当前的年份
        String cYear = sdf.format(new Date()).substring(0,4);
        String cMouth = sdf.format(new Date()).substring(5,7);
        String cDay = sdf.format(new Date()).substring(8,10);
        //得到生日年份
        String birth_Year = birthDay .substring(0,4);
        String birth_Mouth = birthDay .substring(5,7);
        String birth_Day = birthDay .substring(8,10);
        //年月日比较后得到年龄
        int age = Integer.parseInt(cYear) - Integer.parseInt(birth_Year);
        if ((Integer.parseInt(cMouth) - Integer.parseInt(birth_Mouth))<0) {
            age=age-1;
        }else if ((Integer.parseInt(cMouth) - Integer.parseInt(birth_Mouth))==0) {
            if ( (Integer.parseInt(cDay) - Integer.parseInt(birth_Day))>0) {
                age=age-1;
            }else {
                age = Integer.parseInt(cYear) - Integer.parseInt(birth_Year);
            }
        }else if ((Integer.parseInt(cMouth) - Integer.parseInt(birth_Mouth))>0) {
            age = Integer.parseInt(cYear) - Integer.parseInt(birth_Year);
        }
        return age;
    }


    public String extractYearMonthDayOfIdCard(String id) {
        String year = null;
        String month = null;
        String day = null;
        //正则匹配身份证号是否是正确的，15位或者17位数字+数字/x/X
        if (id.matches("^\\d{15}|\\d{17}[\\dxX]$")) {
            year = id.substring(6, 10);
            month = id.substring(10,12);
            day = id.substring(12,14);
        }else {
            System.out.println("身份证号码不匹配！");
            return null;
        }
        return year + "-" + month + "-" + day;
    }

}
